// import React, { useState, useEffect, useRef } from 'react';
// import { Document, Page, pdfjs } from 'react-pdf';
// import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
// import 'react-pdf/dist/esm/Page/TextLayer.css';
// // import AvatarScene from '../../../../components/Avatar/AvatarScene';
// import AvatarsWorld from '../../../../components/Avatars/AvatarsWorld';

// // Fallback options for PDF worker
// const workerOptions = [
//   `https://unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`,
//   `https://unpkg.com/pdfjs-dist@${pdfjs.version}/legacy/build/pdf.worker.min.js`,
//   `https://cdn.jsdelivr.net/npm/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`,
//   `https://cdn.jsdelivr.net/npm/pdfjs-dist@${pdfjs.version}/legacy/build/pdf.worker.min.js`,
//   `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`,
//   `/pdf.worker.min.js`
// ];

// // Function to test worker URL availability
// const testWorkerUrl = async (url) => {
//   try {
//     console.log(`Testing PDF worker URL: ${url}`);
//     const response = await fetch(url, { method: 'HEAD' });
//     return response.ok;
//   } catch {
//     return false;
//   }
// };

// // Function to set up PDF worker with fallbacks
// const setupPdfWorker = async () => {
//   for (const url of workerOptions) {
//     try {
//       console.log(`Attempting to set PDF worker: ${url}`);
//       const isAvailable = await testWorkerUrl(url);
//       if (isAvailable) {
//         pdfjs.GlobalWorkerOptions.workerSrc = url;
//         console.log(`✅ PDF worker successfully set to: ${url}`);
//         return true;
//       }
//     } catch (error) {
//       console.warn(`❌ Failed to load worker from ${url}:`, error);
//     }
//   }
//   console.warn('⚠️ All worker URLs failed, using fake worker (performance may be affected)');
//   pdfjs.GlobalWorkerOptions.workerSrc = false;
//   return false;
// };

// // Initialize worker setup
// let workerInitialized = false;
// const initializeWorker = async () => {
//   if (!workerInitialized) {
//     console.log('Initializing PDF worker...');
//     await setupPdfWorker();
//     workerInitialized = true;
//     console.log('PDF worker initialization complete.');
//   }
// };

// // Error Boundary
// class ErrorBoundary extends React.Component {
//   state = { hasError: false, error: null };

//   static getDerivedStateFromError(error) {
//     return { hasError: true, error };
//   }

//   componentDidCatch(error, errorInfo) {
//     console.error('Error caught by boundary:', error, errorInfo);
//   }

//   render() {
//     if (this.state.hasError) {
//       return (
//         <div
//           className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative"
//           role="alert">
//           <strong className="font-bold">Error:</strong>
//           <span className="block sm:inline">
//             {' '}
//             An unexpected error occurred. Please try again or contact support.
//           </span>
//           <div className="mt-2 text-sm">Details: {this.state.error?.message}</div>
//           <button
//             onClick={() => window.location.reload()}
//             className="mt-2 px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600">
//             Reload Page
//           </button>
//         </div>
//       );
//     }
//     return this.props.children;
//   }
// }

// function Pdf({ fileUrl, fileName, subject, processSelectorId }) {
//   const [file, setFile] = useState(null);
//   const [numPages, setNumPages] = useState(null);
//   const [pageNumber, setPageNumber] = useState(1);
//   const [loading, setLoading] = useState(false);
//   const [error, setError] = useState(null);
//   const [slideData, setSlideData] = useState([]);
//   const [greeting, setGreeting] = useState('');
//   const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
//   const [workerReady, setWorkerReady] = useState(false);
//   const [isFullscreen, setIsFullscreen] = useState(false);
//   const [isPlaying, setIsPlaying] = useState(false);
//   const [isLoading, setIsLoading] = useState(false);
//   const [currentAudio, setCurrentAudio] = useState(null);
//   const [isTransitioning, setIsTransitioning] = useState(false);

//   const pageRef = useRef(null);
//   const audioRef = useRef(null);

//   // Initialize PDF worker
//   useEffect(() => {
//     const init = async () => {
//       try {
//         await initializeWorker();
//         setWorkerReady(true);
//         console.log('Worker ready state set to true.');
//       } catch (err) {
//         console.error('Failed to initialize PDF worker:', err);
//         setError('Failed to initialize PDF viewer');
//       }
//     };
//     init();
//   }, []);

//   // Fullscreen change handler
//   useEffect(() => {
//     const handleFullscreenChange = () => {
//       setIsFullscreen(!!document.fullscreenElement);
//       console.log(`Fullscreen state changed: ${!!document.fullscreenElement}`);
//     };
//     document.addEventListener('fullscreenchange', handleFullscreenChange);
//     return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
//   }, []);

//   // Fetch MongoDB Slide Content
//   useEffect(() => {
//     if (processSelectorId) {
//       console.log(`Fetching MongoDB data for processSelectorId: ${processSelectorId}`);
//       const controller = new AbortController();
//       fetch('https://sasthra.in/api/get-mongo-data', {
//         method: 'POST',
//         headers: { 'Content-Type': 'application/json' },
//         body: JSON.stringify({ process_selector_id: processSelectorId }),
//         signal: controller.signal
//       })
//         .then((res) => {
//           if (!res.ok) throw new Error(`Request failed with status ${res.status}`);
//           return res.json();
//         })
//         .then((data) => {
//           console.log('MongoDB response:', data);
//           if (data.data && data.data.slides) {
//             // Extract greeting
//             setGreeting(data.data.greeting || '');

//             // Convert slides object to array, maintaining order
//             const slidesArray = [];
//             const slidesObj = data.data.slides;

//             // Sort slide keys to ensure proper order (slide1, slide2, etc.)
//             const sortedSlideKeys = Object.keys(slidesObj).sort((a, b) => {
//               const numA = parseInt(a.replace('slide', ''));
//               const numB = parseInt(b.replace('slide', ''));
//               return numA - numB;
//             });

//             sortedSlideKeys.forEach((key) => {
//               if (slidesObj[key]) {
//                 slidesArray.push(slidesObj[key]);
//               }
//             });

//             setSlideData(slidesArray);
//             setCurrentSlideIndex(0);
//             console.log(`Loaded ${slidesArray.length} slides from MongoDB.`);
//             console.log('Greeting:', data.data.greeting);
//           } else if (data.data && data.data.narration) {
//             setSlideData([data.data.narration]);
//             setCurrentSlideIndex(0);
//             console.log('Loaded single narration from MongoDB.');
//           } else {
//             setSlideData([]);
//             console.log('No slides or narration found in MongoDB response.');
//           }
//         })
//         .catch((err) => {
//           if (err.name !== 'AbortError') {
//             console.error('❌ Error fetching MongoDB data:', err);
//             setSlideData([]);
//           }
//         });
//       return () => {
//         console.log('Aborting MongoDB fetch request.');
//         controller.abort();
//       };
//     }
//   }, [processSelectorId]);

//   // Load PDF File
//   useEffect(() => {
//     if (fileUrl && fileName && workerReady) {
//       console.log(`Loading PDF from URL: ${fileUrl}`);
//       fetchPDF(fileUrl);
//     }
//   }, [fileUrl, fileName, workerReady]);

//   // Synchronize slide index with page number
//   useEffect(() => {
//     if (
//       slideData.length > 0 &&
//       currentSlideIndex !== pageNumber - 1 &&
//       pageNumber - 1 <= slideData.length
//     ) {
//       console.log(`Synchronizing slide index to page ${pageNumber}`);
//       setCurrentSlideIndex(pageNumber - 1);
//       console.log(`Updated slide index to ${pageNumber - 1} to match page ${pageNumber}`);
//     }
//   }, [slideData, pageNumber]);

//   // Handle automatic slide navigation during playback
//   useEffect(() => {
//     if (isPlaying && !isTransitioning) {
//       const contentToSpeak = getCurrentSlideContent();
//       if (contentToSpeak) {
//         console.log(`Playing slide ${currentSlideIndex + 1} with content:`, contentToSpeak);
//         speakSlide(contentToSpeak);
//       } else if (pageNumber <= slideData.length + 1) {
//         console.log(`No slide content for slide ${currentSlideIndex + 1}, moving to next slide.`);
//         setCurrentSlideIndex((prev) => prev + 1);
//       } else if (pageNumber < numPages) {
//         console.log(`No slide content for slide ${currentSlideIndex + 1}, moving to next page.`);
//         goToNextPage();
//       } else {
//         console.log('Reached end of presentation or no content, stopping playback.');
//         if (audioRef.current) {
//           audioRef.current.pause();
//           audioRef.current.currentTime = 0;
//           console.log('Paused audio due to end of presentation.');
//         }
//         setIsPlaying(false);
//       }
//     }
//   }, [isPlaying, currentSlideIndex, slideData, numPages, isTransitioning]);

//   // Cleanup audio on component unmount
//   useEffect(() => {
//     return () => {
//       console.log('Cleaning up audio on unmount.');
//       if (audioRef.current) {
//         audioRef.current.pause();
//         audioRef.current.currentTime = 0;
//       }
//     };
//   }, []);

//   // Helper function to get current slide content
//   const getCurrentSlideContent = () => {
//     // For page 1 (index 0), return the greeting
//     if (pageNumber === 1) {
//       return greeting || '';
//     }

//     // For page 2 onwards, return corresponding MongoDB slide data (page 2 = slideData[0], page 3 = slideData[1], etc.)
//     const slideIndex = pageNumber - 2;
//     return slideData[slideIndex] || '';
//   };

//   const fetchPDF = async (url) => {
//     setLoading(true);
//     setError(null);
//     console.log(`Fetching PDF from: ${url}`);
//     const controller = new AbortController();
//     try {
//       const res = await fetch(url, {
//         signal: controller.signal,
//         headers: { Accept: 'application/pdf' }
//       });
//       if (!res.ok) throw new Error(`PDF fetch failed: ${res.status} ${res.statusText}`);
//       const contentType = res.headers.get('content-type');
//       if (contentType && !contentType.includes('application/pdf')) {
//         console.warn('⚠️ Response may not be a PDF file:', contentType);
//       }
//       const blob = await res.blob();
//       const objectUrl = URL.createObjectURL(blob);
//       setFile(objectUrl);
//       console.log(`PDF loaded successfully, object URL: ${objectUrl}`);
//       return () => {
//         if (objectUrl) {
//           console.log(`Revoking object URL: ${objectUrl}`);
//           URL.revokeObjectURL(objectUrl);
//         }
//         controller.abort();
//       };
//     } catch (err) {
//       if (err.name !== 'AbortError') {
//         console.error('❌ Error loading PDF:', err.message);
//         setError(`Failed to load PDF: ${err.message}`);
//       }
//     } finally {
//       setLoading(false);
//       console.log('PDF fetch complete, loading state set to false.');
//     }
//   };

//   const goToPrevPage = () => {
//     if (pageNumber > 1) {
//       const newPageNumber = pageNumber - 1;
//       setPageNumber(newPageNumber);
//       if (audioRef.current) {
//         audioRef.current.pause();
//         audioRef.current.currentTime = 0;
//         console.log('Paused audio for previous page navigation.');
//       }
//       setIsPlaying(false);

//       // Add 2 second delay before updating slide index
//       setIsTransitioning(true);
//       setTimeout(() => {
//         setCurrentSlideIndex(newPageNumber - 1);
//         setIsTransitioning(false);
//         console.log(`Navigated to previous page: ${newPageNumber}`);
//       }, 2000);
//     }
//   };

//   const goToNextPage = () => {
//     if (pageNumber < numPages) {
//       const newPageNumber = pageNumber + 1;
//       setPageNumber(newPageNumber);
//       if (audioRef.current) {
//         audioRef.current.pause();
//         audioRef.current.currentTime = 0;
//         console.log('Paused audio for next page navigation.');
//       }

//       // Add 2 second delay before updating slide index
//       setIsTransitioning(true);
//       setTimeout(() => {
//         setCurrentSlideIndex(newPageNumber - 1);
//         setIsTransitioning(false);
//         console.log(`Navigated to next page: ${newPageNumber}`);
//       }, 2000);
//     } else {
//       if (audioRef.current) {
//         audioRef.current.pause();
//         audioRef.current.currentTime = 0;
//         console.log('Paused audio, reached last page.');
//       }
//       setIsPlaying(false);
//       console.log('Reached last page, stopped playback.');
//     }
//   };

//   const handlePlayPause = () => {
//     if (isPlaying) {
//       if (audioRef.current) {
//         audioRef.current.pause();
//         console.log('Paused audio playback.');
//       }
//       setIsPlaying(false);
//     } else {
//       if (audioRef.current && audioRef.current.paused && !audioRef.current.ended) {
//         audioRef.current.play();
//         setIsPlaying(true);
//         console.log('Resumed audio playback.');
//       } else {
//         const contentToSpeak = getCurrentSlideContent();
//         if (contentToSpeak) {
//           console.log(`Starting playback for page ${pageNumber}`);
//           setIsPlaying(true);
//         } else if (pageNumber < numPages) {
//           console.log('No slide content, moving to next page.');
//           goToNextPage();
//         }
//       }
//     }
//   };

//   const speakSlide = async (content) => {
//     if (!content) {
//       console.log('No content to speak, checking next page.');
//       if (pageNumber < numPages) {
//         goToNextPage();
//       } else {
//         setIsPlaying(false);
//         console.log('No more pages, stopping playback.');
//       }
//       return;
//     }

//     setIsLoading(true);
//     console.log(`Speaking page ${pageNumber} with content:`, content);

//     try {
//       const response = await fetch('https://sasthra.in/tts', {
//         method: 'POST',
//         headers: { 'Content-Type': 'application/json' },
//         body: JSON.stringify({ text: content })
//       });

//       if (!response.ok) {
//         throw new Error(`TTS request failed: ${response.status}`);
//       }
//       console.log(response, "response is here");
//       const audioBlob = await response.blob();
//       const audioUrl = URL.createObjectURL(audioBlob);
//       const audio = new Audio(audioUrl);

//       // Clean up previous audio if any
//       if (audioRef.current) {
//         audioRef.current.pause();
//         audioRef.current.currentTime = 0;
//         URL.revokeObjectURL(audioRef.current.src);
//         console.log('Cleaned up previous audio.');
//       }

//       audioRef.current = audio;
//       setCurrentAudio(audio);

//       audio.onended = () => {
//         console.log(`Audio ended for page ${pageNumber}`);
//         URL.revokeObjectURL(audioUrl);

//         if (pageNumber < numPages && isPlaying) {
//           goToNextPage();
//         } else {
//           setIsPlaying(false);
//           console.log('No more pages or playback stopped.');
//         }
//       };

//       audio.onerror = () => {
//         console.error('Audio playback error');
//         URL.revokeObjectURL(audioUrl);
//         setIsPlaying(false);
//         setIsLoading(false);
//         setCurrentAudio(null);
//       };

//       await audio.play();
//       setIsPlaying(true);
//       console.log(`Playing audio for page ${pageNumber}`);
//     } catch (error) {
//       console.error('TTS Error:', error);
//       setError(`TTS Error: ${error.message}`);
//       setIsPlaying(false);
//     } finally {
//       setIsLoading(false);
//       console.log('TTS request complete, loading state set to false.');
//     }
//   };

//   const handleDocumentLoadSuccess = ({ numPages }) => {
//     console.log(`✅ PDF loaded successfully with ${numPages} pages`);
//     setNumPages(numPages);
//     setError(null);
//   };

//   const handleDocumentLoadError = (error) => {
//     console.error('❌ Document load error:', error);
//     setError(`Error loading PDF: ${error.message || 'Unknown error'}`);
//   };

//   const handlePageLoadError = (error) => {
//     console.error('❌ Page load error:', error);
//     setError(`Error loading page: ${error.message || 'Unknown error'}`);
//   };

//   const retryLoadPdf = () => {
//     setError(null);
//     console.log('Retrying PDF load.');
//     if (fileUrl) {
//       fetchPDF(fileUrl);
//     }
//   };

//   const toggleFullscreen = async () => {
//     const container = document.getElementById('pdf-container');
//     if (!document.fullscreenElement && container) {
//       try {
//         await container.requestFullscreen();
//         console.log('Entered fullscreen mode.');
//       } catch (err) {
//         console.error(`Error attempting to enable fullscreen: ${err.message}`);
//       }
//     } else if (document.fullscreenElement) {
//       try {
//         await document.exitFullscreen();
//         console.log('Exited fullscreen mode.');
//       } catch (err) {
//         console.error(`Error attempting to exit fullscreen: ${err.message}`);
//       }
//     }
//   };

//   if (!workerReady) {
//     return (
//       <div className="bg-gray-900 min-h-screen text-white p-6 flex items-center justify-center">
//         <div className="text-center">
//           <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
//           <p className="text-gray-300">Initializing PDF viewer...</p>
//         </div>
//       </div>
//     );
//   }

//   return (
//     <ErrorBoundary>
//   <div className="bg-gray-900 min-h-screen text-white" id="pdf-container">
//     {/* Header with Title and Controls */}
//     <div className="bg-gray-800 px-6 py-4 border-b border-gray-700">
//       <div className="flex items-center justify-between">
//         {/* Title */}
//         <h1 className="text-2xl font-bold text-white">
//           {subject ? `Subject: ${subject}` : 'Learning Viewer'}
//         </h1>
//         {/* Top Controls */}
//         <div className="flex items-center space-x-4">
//           {/* Navigation Controls */}
//           <div className="flex items-center space-x-2">
//             <button
//               onClick={goToPrevPage}
//               disabled={pageNumber === 1 || isTransitioning}
//               className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-1">
//               <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
//                 <path
//                   fillRule="evenodd"
//                   d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
//                   clipRule="evenodd"
//                 />
//               </svg>
//               <span>Prev</span>
//             </button>
//             <span className="text-sm text-gray-300 px-3">
//               {currentSlideIndex + 1} / {slideData.length + 1 || 1}
//             </span>
//             <button
//               onClick={goToNextPage}
//               disabled={pageNumber === numPages || isTransitioning}
//               className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-1">
//               <span>Next</span>
//               <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
//                 <path
//                   fillRule="evenodd"
//                   d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
//                   clipRule="evenodd"
//                 />
//               </svg>
//             </button>
//           </div>
//           {/* Voice Controls */}
//           <div className="flex items-center space-x-3">
//             {/* Play/Pause Button */}
//             <button
//               onClick={handlePlayPause}
//               disabled={isLoading || isTransitioning || !getCurrentSlideContent()}
//               className="flex items-center space-x-2 px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded-lg transition-colors">
//               {isLoading ? (
//                 <>
//                   <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
//                   <span>Loading...</span>
//                 </>
//               ) : isTransitioning ? (
//                 <>
//                   <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
//                   <span>Transitioning...</span>
//                 </>
//               ) : isPlaying ? (
//                 <>
//                   <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
//                     <path
//                       fillRule="evenodd"
//                       d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z"
//                       clipRule="evenodd"
//                     />
//                   </svg>
//                   <span>Pause</span>
//                 </>
//               ) : (
//                 <>
//                   <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
//                     <path
//                       fillRule="evenodd"
//                       d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
//                       clipRule="evenodd"
//                     />
//                   </svg>
//                   <span>Play</span>
//                 </>
//               )}
//             </button>
//             {/* Fullscreen Button */}
//             <button
//               onClick={toggleFullscreen}
//               className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded text-sm transition-colors flex items-center space-x-1">
//               <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
//                 {isFullscreen ? (
//                   <path
//                     fillRule="evenodd"
//                     d="M10 2L3 9l1.5 1.5L9 6v8l-4.5-4.5L3 11l7-7z"
//                     clipRule="evenodd"
//                   />
//                 ) : (
//                   <path
//                     fillRule="evenodd"
//                     d="M3 5a1 1 0 011-1h2a1 1 0 110 2H5v1a1 1 0 01-2 0V5zm14 0a1 1 0 01-1-1h-2a1 1 0 110-2h2v1a1 1 0 012 0v2zm-14 10a1 1 0 001 1h2a1 1 0 100-2H5v-1a1 1 0 00-2 0v2zm14 0a1 1 0 01-1 1h-2a1 1 0 110-2h1v-1a1 1 0 012 0v2z"
//                     clipRule="evenodd"
//                   />
//                 )}
//               </svg>
//               <span>{isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}</span>
//             </button>
//           </div>
//         </div>
//       </div>
//     </div>
//     <div className="flex h-[calc(100vh-80px)]">
//       {/* Left Side - PDF Display */}
//       <div className="flex-1 p-6 overflow-auto">
//         {/* Error Display */}
//         {error && (
//           <div className="mb-6 p-4 bg-red-900/50 border border-red-700 rounded-lg">
//             <p className="text-red-300 mb-2">{error}</p>
//             <button
//               onClick={retryLoadPdf}
//               className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded text-sm transition-colors">
//               Retry Loading PDF
//             </button>
//           </div>
//         )}
//         {/* Loading Indicator */}
//         {loading && (
//           <div className="mb-6 p-4 bg-blue-900/50 border border-blue-700 rounded-lg">
//             <div className="flex items-center">
//               <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-400 mr-3"></div>
//               <p className="text-blue-300">Loading PDF...</p>
//             </div>
//           </div>
//         )}
//         {/* PDF Viewer */}
//         {file && !error && (
//           <div className="bg-gray-800 p-4 rounded-xl shadow-lg relative" ref={pageRef}>
//             <Document
//               file={file}
//               onLoadSuccess={handleDocumentLoadSuccess}
//               onLoadError={handleDocumentLoadError}
//               loading={
//                 <div className="flex items-center justify-center py-8">
//                   <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mr-3"></div>
//                   <span className="text-gray-300">Loading document...</span>
//                 </div>
//               }
//               error={
//                 <div className="text-center py-8 text-red-400">
//                   <p>Failed to load PDF document.</p>
//                   <button
//                     onClick={retryLoadPdf}
//                     className="mt-2 px-4 py-2 bg-red-600 hover:bg-red-700 rounded text-sm">
//                     Retry
//                   </button>
//                 </div>
//               }>
//               <div className="relative"> {/* Make the container relative for absolute positioning */}
//                 <Page
//                   key={`page_${pageNumber}`}
//                   pageNumber={pageNumber}
//                   width={Math.min(800, window.innerWidth * 0.6 - 100)}
//                   className="shadow-lg mx-auto"
//                   onLoadError={handlePageLoadError}
//                   loading={
//                     <div className="flex items-center justify-center py-8">
//                       <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mr-3"></div>
//                       <span className="text-gray-300">Loading page...</span>
//                     </div>
//                   }
//                   error={
//                     <div className="text-center py-8 text-red-400">
//                       <p>Failed to load page {pageNumber}.</p>
//                     </div>
//                   }
//                 />
//                 {/* AvatarWorld Overlay */}
//                 <div className="absolute bottom-0 right-0 z-10">  {/* Added z-10 */}
//                   <AvatarsWorld />
//                 </div>
//               </div>
//             </Document>
//             {/* PDF Page Info */}
//             <div className="flex justify-center items-center mt-4">
//               <div className="flex items-center space-x-2">
//                 <span className="text-sm text-gray-400">
//                   Page {pageNumber} of {numPages || 1}
//                 </span>
//                 {numPages && numPages > 1 && (
//                   <select
//                     value={pageNumber}
//                     onChange={(e) => {
//                       const newPage = Number(e.target.value);
//                       setPageNumber(newPage);
//                       if (audioRef.current) {
//                         audioRef.current.pause();
//                         audioRef.current.currentTime = 0;
//                         console.log('Paused audio due to page selection.');
//                       }
//                       setIsPlaying(false);
//                       setIsTransitioning(true);
//                       setTimeout(() => {
//                         setCurrentSlideIndex(newPage - 1);
//                         setIsTransitioning(false);
//                         console.log(`Selected page ${newPage}`);
//                       }, 2000);
//                     }}
//                     className="bg-gray-700 text-white text-sm rounded px-2 py-1 border border-gray-600">
//                     {Array.from({ length: numPages }, (_, i) => i + 1).map((page) => (
//                       <option key={page} value={page}>
//                         {page}
//                       </option>
//                     ))}
//                   </select>
//                 )}
//               </div>
//             </div>
//           </div>
//         )}
//         {/* No PDF loaded state */}
//         {!file && !loading && !error && (
//           <div className="text-center py-12 text-gray-400">
//             <p>No PDF file loaded. Please provide a valid file URL.</p>
//           </div>
//         )}
//       </div>
//       {/* Right Side - Narration Content */}
//       <div className="w-96 bg-gray-800 border-l border-gray-700 flex flex-col">
//         {/* Narration Content */}
//         <div className="flex-1 p-6 overflow-auto">
//           <div className="mb-4">
//             <h3 className="text-xl font-semibold mb-4 text-white">Narration</h3>

//             <div className="bg-gray-700 rounded-lg p-6 min-h-96">
//               {slideData.length > 0 ? (
//                 <div className="text-gray-200 leading-relaxed">
//                   {slideData[currentSlideIndex] ? (
//                     <p className="text-base">{slideData[currentSlideIndex]}</p>
//                   ) : (
//                     <p className="text-gray-400 italic">
//                       No narration available for this slide.
//                     </p>
//                   )}
//                 </div>
//               ) : (
//                 <p className="text-gray-400 italic">No narration available for this slide.</p>
//               )}
//             </div>
//           </div>
//           {/* Additional Slide Info */}
//           <div className="bg-gray-700 rounded-lg p-4">
//             <div className="flex justify-between items-center text-sm text-gray-400">
//               <span>Current Slide: {currentSlideIndex + 1}</span>
//               <span>Total Slides: {slideData.length + 1 || 1}</span>
//             </div>
//             <div className="mt-2">
//               <div className="w-full bg-gray-600 rounded-full h-2">
//                 <div
//                   className="bg-blue-500 h-2 rounded-full transition-all duration-300"
//                   style={{
//                     width:
//                       slideData.length > 0 || greeting
//                         ? `${((currentSlideIndex + 1) / (slideData.length + 1)) * 100}%`
//                         : '0%'
//                   }}></div>
//               </div>
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   </div>
// </ErrorBoundary>
//   );
// }

// export default Pdf;


import React, { useState, useEffect, useRef } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';
import AvatarsWorld from '../../../../components/Avatars/AvatarsWorld';

// Fallback options for PDF worker
const workerOptions = [
  `https://unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`,
  `https://unpkg.com/pdfjs-dist@${pdfjs.version}/legacy/build/pdf.worker.min.js`,
  `https://cdn.jsdelivr.net/npm/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.mjs`,
  `https://cdn.jsdelivr.net/npm/pdfjs-dist@${pdfjs.version}/legacy/build/pdf.worker.min.js`,
  `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`,
  `/pdf.worker.min.js`
];

// Function to test worker URL availability
const testWorkerUrl = async (url) => {
  try {
    console.log(`Testing PDF worker URL: ${url}`);
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch {
    return false;
  }
};

// Function to set up PDF worker with fallbacks
const setupPdfWorker = async () => {
  for (const url of workerOptions) {
    try {
      console.log(`Attempting to set PDF worker: ${url}`);
      const isAvailable = await testWorkerUrl(url);
      if (isAvailable) {
        pdfjs.GlobalWorkerOptions.workerSrc = url;
        console.log(`✅ PDF worker successfully set to: ${url}`);
        return true;
      }
    } catch (error) {
      console.warn(`❌ Failed to load worker from ${url}:`, error);
    }
  }
  console.warn('⚠️ All worker URLs failed, using fake worker (performance may be affected)');
  pdfjs.GlobalWorkerOptions.workerSrc = false;
  return false;
};

// Initialize worker setup
let workerInitialized = false;
const initializeWorker = async () => {
  if (!workerInitialized) {
    console.log('Initializing PDF worker...');
    await setupPdfWorker();
    workerInitialized = true;
    console.log('PDF worker initialization complete.');
  }
};

// Error Boundary
class ErrorBoundary extends React.Component {
  state = { hasError: false, error: null };

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div
          className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative"
          role="alert">
          <strong className="font-bold">Error:</strong>
          <span className="block sm:inline">
            {' '}
            An unexpected error occurred. Please try again or contact support.
          </span>
          <div className="mt-2 text-sm">Details: {this.state.error?.message}</div>
          <button
            onClick={() => window.location.reload()}
            className="mt-2 px-3 py-1 bg-red-500 text-white rounded text-sm hover:bg-red-600">
            Reload Page
          </button>
        </div>
      );
    }
    return this.props.children;
  }
}

function Pdf({ fileUrl, fileName, subject, processSelectorId }) {
  const [file, setFile] = useState(null);
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [slideData, setSlideData] = useState([]);
  const [greeting, setGreeting] = useState('');
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
  const [workerReady, setWorkerReady] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentAudio, setCurrentAudio] = useState(null);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const pageRef = useRef(null);
  const audioRef = useRef(null);

  // Initialize PDF worker
  useEffect(() => {
    const init = async () => {
      try {
        await initializeWorker();
        setWorkerReady(true);
        console.log('Worker ready state set to true.');
      } catch (err) {
        console.error('Failed to initialize PDF worker:', err);
        setError('Failed to initialize PDF viewer');
      }
    };
    init();
  }, []);

  // Fullscreen change handler
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
      console.log(`Fullscreen state changed: ${!!document.fullscreenElement}`);
    };
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);

  // Fetch MongoDB Slide Content
  useEffect(() => {
    if (processSelectorId) {
      console.log(`Fetching MongoDB data for processSelectorId: ${processSelectorId}`);
      const controller = new AbortController();
      fetch('https://sasthra.in/api/get-mongo-data', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ process_selector_id: processSelectorId }),
        signal: controller.signal
      })
        .then((res) => {
          if (!res.ok) throw new Error(`Request failed with status ${res.status}`);
          return res.json();
        })
        .then((data) => {
          console.log('MongoDB response:', data);
          if (data.data && data.data.slides) {
            // Extract greeting
            setGreeting(data.data.greeting || '');

            // Convert slides object to array, maintaining order
            const slidesArray = [];
            const slidesObj = data.data.slides;

            // Sort slide keys to ensure proper order (slide1, slide2, etc.)
            const sortedSlideKeys = Object.keys(slidesObj).sort((a, b) => {
              const numA = parseInt(a.replace('slide', ''));
              const numB = parseInt(b.replace('slide', ''));
              return numA - numB;
            });

            sortedSlideKeys.forEach((key) => {
              if (slidesObj[key]) {
                slidesArray.push(slidesObj[key]);
              }
            });

            setSlideData(slidesArray);
            setCurrentSlideIndex(0);
            console.log(`Loaded ${slidesArray.length} slides from MongoDB.`);
            console.log('Greeting:', data.data.greeting);
          } else if (data.data && data.data.narration) {
            setSlideData([data.data.narration]);
            setCurrentSlideIndex(0);
            console.log('Loaded single narration from MongoDB.');
          } else {
            setSlideData([]);
            console.log('No slides or narration found in MongoDB response.');
          }
        })
        .catch((err) => {
          if (err.name !== 'AbortError') {
            console.error('❌ Error fetching MongoDB data:', err);
            setSlideData([]);
          }
        });
      return () => {
        console.log('Aborting MongoDB fetch request.');
        controller.abort();
      };
    }
  }, [processSelectorId]);

  // Load PDF File
  useEffect(() => {
    if (fileUrl && fileName && workerReady) {
      console.log(`Loading PDF from URL: ${fileUrl}`);
      fetchPDF(fileUrl);
    }
  }, [fileUrl, fileName, workerReady]);

  // Synchronize slide index with page number
  useEffect(() => {
    // Page 1 corresponds to greeting (slide index 0), page 2 corresponds to slideData[0], etc.
    if (slideData.length > 0 && pageNumber !== currentSlideIndex + 1 && pageNumber <= slideData.length + 1) {
      console.log(`Synchronizing slide index to page ${pageNumber}`);
      setCurrentSlideIndex(pageNumber - 1);
      console.log(`Updated slide index to ${pageNumber - 1} to match page ${pageNumber}`);
    }
  }, [slideData, pageNumber]);

  // Handle automatic slide navigation during playback
  useEffect(() => {
    if (isPlaying && !isTransitioning) {
      const contentToSpeak = getCurrentSlideContent();
      if (contentToSpeak) {
        console.log(`Playing slide ${currentSlideIndex + 1} with content:`, contentToSpeak);
        speakSlide(contentToSpeak);
      } else if (pageNumber <= slideData.length + 1) {
        console.log(`No slide content for slide ${currentSlideIndex + 1}, moving to next slide.`);
        goToNextPage();
      } else {
        console.log('Reached end of presentation or no content, stopping playback.');
        if (audioRef.current) {
          audioRef.current.pause();
          audioRef.current.currentTime = 0;
          console.log('Paused audio due to end of presentation.');
        }
        setIsPlaying(false);
      }
    }
  }, [isPlaying, currentSlideIndex, slideData, numPages, isTransitioning]);

  // Cleanup audio on component unmount
  useEffect(() => {
    return () => {
      console.log('Cleaning up audio on unmount.');
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
      }
    };
  }, []);

  // Helper function to get current slide content
  const getCurrentSlideContent = () => {
    // For page 1 (index 0), return the greeting
    if (pageNumber === 1) {
      return greeting || '';
    }

    // For page 2 onwards, return corresponding MongoDB slide data (page 2 = slideData[0], page 3 = slideData[1], etc.)
    const slideIndex = pageNumber - 2;
    return slideData[slideIndex] || '';
  };

  const fetchPDF = async (url) => {
    setLoading(true);
    setError(null);
    console.log(`Fetching PDF from: ${url}`);
    const controller = new AbortController();
    try {
      const res = await fetch(url, {
        signal: controller.signal,
        headers: { Accept: 'application/pdf' }
      });
      if (!res.ok) throw new Error(`PDF fetch failed: ${res.status} ${res.statusText}`);
      const contentType = res.headers.get('content-type');
      if (contentType && !contentType.includes('application/pdf')) {
        console.warn('⚠️ Response may not be a PDF file:', contentType);
      }
      const blob = await res.blob();
      const objectUrl = URL.createObjectURL(blob);
      setFile(objectUrl);
      console.log(`PDF loaded successfully, object URL: ${objectUrl}`);
      return () => {
        if (objectUrl) {
          console.log(`Revoking object URL: ${objectUrl}`);
          URL.revokeObjectURL(objectUrl);
        }
        controller.abort();
      };
    } catch (err) {
      if (err.name !== 'AbortError') {
        console.error('❌ Error loading PDF:', err.message);
        setError(`Failed to load PDF: ${err.message}`);
      }
    } finally {
      setLoading(false);
      console.log('PDF fetch complete, loading state set to false.');
    }
  };

  const goToPrevPage = () => {
    if (pageNumber > 1) {
      const newPageNumber = pageNumber - 1;
      setPageNumber(newPageNumber);
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
        console.log('Paused audio for previous page navigation.');
      }
      setIsPlaying(false);

      // Add 2 second delay before updating slide index
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentSlideIndex(newPageNumber - 1);
        setIsTransitioning(false);
        console.log(`Navigated to previous page: ${newPageNumber}`);
      }, 2000);
    }
  };

  const goToNextPage = () => {
    if (pageNumber < numPages) {
      const newPageNumber = pageNumber + 1;
      setPageNumber(newPageNumber);
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
        console.log('Paused audio for next page navigation.');
      }

      // Add 2 second delay before updating slide index
      setIsTransitioning(true);
      setTimeout(() => {
        setCurrentSlideIndex(newPageNumber - 1);
        setIsTransitioning(false);
        console.log(`Navigated to next page: ${newPageNumber}`);
      }, 2000);
    } else {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.currentTime = 0;
        console.log('Paused audio, reached last page.');
      }
      setIsPlaying(false);
      console.log('Reached last page, stopped playback.');
    }
  };

  const handlePlayPause = () => {
    if (isPlaying) {
      if (audioRef.current) {
        audioRef.current.pause();
        console.log('Paused audio playback.');
      }
      setIsPlaying(false);
    } else {
      if (audioRef.current && audioRef.current.paused && !audioRef.current.ended) {
        audioRef.current.play();
        setIsPlaying(true);
        console.log('Resumed audio playback.');
      } else {
        const contentToSpeak = getCurrentSlideContent();
        if (contentToSpeak) {
          console.log(`Starting playback for page ${pageNumber}`);
          setIsPlaying(true);
        } else if (pageNumber < numPages) {
          console.log('No slide content, moving to next page.');
          goToNextPage();
        }
      }
    }
  };

  // const speakSlide = async (content) => {
  //   if (!content) {
  //     console.log('No content to speak, checking next page.');
  //     if (pageNumber < numPages) {
  //       goToNextPage();
  //     } else {
  //       setIsPlaying(false);
  //       console.log('No more pages, stopping playback.');
  //     }
  //     return;
  //   }

  //   setIsLoading(true);
  //   console.log(`Speaking page ${pageNumber} with content:`, content);

  //   try {
  //     const response = await fetch('https://sasthra.in/tts', {
  //       method: 'POST',
  //       headers: { 'Content-Type': 'application/json' },
  //       body: JSON.stringify({ text: content })
  //     });

  //     if (!response.ok) {
  //       throw new Error(`TTS request failed: ${response.status}`);
  //     }
  //     console.log(response, "response is here");
  //     const audioBlob = await response.blob();
  //     const audioUrl = URL.createObjectURL(audioBlob);
  //     const audio = new Audio(audioUrl);

  //     // Clean up previous audio if any
  //     if (audioRef.current) {
  //       audioRef.current.pause();
  //       audioRef.current.currentTime = 0;
  //       URL.revokeObjectURL(audioRef.current.src);
  //       console.log('Cleaned up previous audio.');
  //     }

  //     audioRef.current = audio;
  //     setCurrentAudio(audio);

  //     audio.onended = () => {
  //       console.log(`Audio ended for page ${pageNumber}`);
  //       URL.revokeObjectURL(audioUrl);

  //       if (pageNumber < numPages && isPlaying) {
  //         goToNextPage();
  //       } else {
  //         setIsPlaying(false);
  //         console.log('No more pages or playback stopped.');
  //       }
  //     };

  //     audio.onerror = () => {
  //       console.error('Audio playback error');
  //       URL.revokeObjectURL(audioUrl);
  //       setIsPlaying(false);
  //       setIsLoading(false);
  //       setCurrentAudio(null);
  //     };

  //     await audio.play();
  //     setIsPlaying(true);
  //     console.log(`Playing audio for page ${pageNumber}`);
  //   } catch (error) {
  //     console.error('TTS Error:', error);
  //     setError(`TTS Error: ${error.message}`);
  //     setIsPlaying(false);
  //   } finally {
  //     setIsLoading(false);
  //     console.log('TTS request complete, loading state set to false.');
  //   }
  // };


  const speakSlide = async (content) => {
  if (!content) {
    console.log('No content to speak, checking next page.');
    if (pageNumber < numPages) {
      goToNextPage();
    } else {
      setIsPlaying(false);
      console.log('No more pages, stopping playback.');
    }
    return;
  }

  setIsLoading(true);
  console.log(`Speaking page ${pageNumber} with content:`, content);

  try {
    const response = await fetch('https://sasthra.in/tts', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ text: content })
    });

    if (!response.ok) {
      throw new Error(`TTS request failed: ${response.status}`);
    }

    const ttsData = await response.json();
    console.log('TTS response received:', ttsData);
    console.log('Received phonemes for current audio file:', ttsData.phonemes);

    // Convert base64 audio to blob
    const audioBytes = atob(ttsData.audio.replace(/^data:audio\/mpeg;base64,/, ''));
    const arrayBuffer = new ArrayBuffer(audioBytes.length);
    const uint8Array = new Uint8Array(arrayBuffer);
    for (let i = 0; i < audioBytes.length; i++) {
      uint8Array[i] = audioBytes.charCodeAt(i);
    }
    const audioBlob = new Blob([arrayBuffer], { type: ttsData.mimetype });
    const audioUrl = URL.createObjectURL(audioBlob);
    const audio = new Audio(audioUrl);

    // Clean up previous audio if any
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      URL.revokeObjectURL(audioRef.current.src);
      console.log('Cleaned up previous audio.');
    }

    audioRef.current = audio;
    setCurrentAudio(audio);

    audio.onended = () => {
      console.log(`Audio ended for page ${pageNumber}`);
      URL.revokeObjectURL(audioUrl);

      if (pageNumber < numPages && isPlaying) {
        goToNextPage();
      } else {
        setIsPlaying(false);
        console.log('No more pages or playback stopped.');
      }
    };

    audio.onerror = () => {
      console.error('Audio playback error');
      URL.revokeObjectURL(audioUrl);
      setIsPlaying(false);
      setIsLoading(false);
      setCurrentAudio(null);
    };

    await audio.play();
    setIsPlaying(true);
    console.log(`Playing audio for page ${pageNumber}`);
  } catch (error) {
    console.error('TTS Error:', error);
    setError(`TTS Error: ${error.message}`);
    setIsPlaying(false);
  } finally {
    setIsLoading(false);
    console.log('TTS request complete, loading state set to false.');
  }
};

  const handleDocumentLoadSuccess = ({ numPages }) => {
    console.log(`✅ PDF loaded successfully with ${numPages} pages`);
    setNumPages(numPages);
    setError(null);
  };

  const handleDocumentLoadError = (error) => {
    console.error('❌ Document load error:', error);
    setError(`Error loading PDF: ${error.message || 'Unknown error'}`);
  };

  const handlePageLoadError = (error) => {
    console.error('❌ Page load error:', error);
    setError(`Error loading page: ${error.message || 'Unknown error'}`);
  };

  const retryLoadPdf = () => {
    setError(null);
    console.log('Retrying PDF load.');
    if (fileUrl) {
      fetchPDF(fileUrl);
    }
  };

  const toggleFullscreen = async () => {
    const container = document.getElementById('pdf-container');
    if (!document.fullscreenElement && container) {
      try {
        await container.requestFullscreen();
        console.log('Entered fullscreen mode.');
      } catch (err) {
        console.error(`Error attempting to enable fullscreen: ${err.message}`);
      }
    } else if (document.fullscreenElement) {
      try {
        await document.exitFullscreen();
        console.log('Exited fullscreen mode.');
      } catch (err) {
        console.error(`Error attempting to exit fullscreen: ${err.message}`);
      }
    }
  };

  if (!workerReady) {
    return (
      <div className="bg-gray-900 min-h-screen text-white p-6 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-300">Initializing PDF viewer...</p>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="bg-gray-900 min-h-screen text-white" id="pdf-container">
        {/* Header with Title and Controls */}
        <div className="bg-gray-800 px-6 py-4 border-b border-gray-700 shadow-sm">
          <div className="flex items-center justify-between">
            {/* Title */}
            <h1 className="text-2xl font-bold text-white">
              {subject ? `Subject: ${subject}` : 'Learning Viewer'}
            </h1>
            {/* Top Controls */}
            <div className="flex items-center space-x-4">
              {/* Navigation Controls */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={goToPrevPage}
                  disabled={pageNumber === 1 || isTransitioning}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-1 text-white"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <span>Prev</span>
                </button>
                <span className="text-sm text-gray-300 px-3">
                  {currentSlideIndex + 1} / {(slideData.length + 1) || 1}
                </span>
                <button
                  onClick={goToNextPage}
                  disabled={pageNumber === numPages || isTransitioning}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-1 text-white"
                >
                  <span>Next</span>
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              </div>
              {/* Voice Controls */}
              <div className="flex items-center space-x-3">
                {/* Play/Pause Button */}
                <button
                  onClick={handlePlayPause}
                  disabled={isLoading || isTransitioning || !getCurrentSlideContent()}
                  className="flex items-center space-x-2 px-6 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed rounded-lg transition-colors text-white"
                >
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                      <span>Loading...</span>
                    </>
                  ) : isTransitioning ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                      <span>Transitioning...</span>
                    </>
                  ) : isPlaying ? (
                    <>
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path
                          fillRule="evenodd"
                          d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z"
                          clipRule="evenodd"
                        />
                      </svg>
                      <span>Pause</span>
                    </>
                  ) : (
                    <>
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path
                          fillRule="evenodd"
                          d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z"
                          clipRule="evenodd"
                        />
                      </svg>
                      <span>Play</span>
                    </>
                  )}
                </button>
                {/* Fullscreen Button */}
                <button
                  onClick={toggleFullscreen}
                  className="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded text-sm transition-colors flex items-center space-x-1 text-white"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    {isFullscreen ? (
                      <path
                        fillRule="evenodd"
                        d="M10 2L3 9l1.5 1.5L9 6v8l-4.5-4.5L3 11l7-7z"
                        clipRule="evenodd"
                      />
                    ) : (
                      <path
                        fillRule="evenodd"
                        d="M3 5a1 1 0 011-1h2a1 1 0 110 2H5v1a1 1 0 01-2 0V5zm14 0a1 1 0 01-1-1h-2a1 1 0 110-2h2v1a1 1 0 012 0v2zm-14 10a1 1 0 001 1h2a1 1 0 100-2H5v-1a1 1 0 00-2 0v2zm14 0a1 1 0 01-1 1h-2a1 1 0 110-2h1v-1a1 1 0 012 0v2z"
                        clipRule="evenodd"
                      />
                    )}
                  </svg>
                  <span>{isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
        <div className="flex h-[calc(100vh-80px)]">
          {/* Left Side - PDF Display */}
          <div className="flex-1 p-6 overflow-auto">
            {/* Error Display */}
            {error && (
              <div className="mb-6 p-4 bg-red-900/50 border border-red-700 rounded-lg">
                <p className="text-red-300 mb-2">{error}</p>
                <button
                  onClick={retryLoadPdf}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 rounded text-sm transition-colors text-white"
                >
                  Retry Loading PDF
                </button>
              </div>
            )}
            {/* Loading Indicator */}
            {loading && (
              <div className="mb-6 p-4 bg-blue-900/50 border border-blue-700 rounded-lg">
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-400 mr-3"></div>
                  <p className="text-blue-300">Loading PDF...</p>
                </div>
              </div>
            )}
            {/* PDF Viewer */}
            {file && !error && (
              <div className="bg-gray-800 p-4 rounded-xl shadow-lg border border-gray-700" ref={pageRef}>
                <Document
                  file={file}
                  onLoadSuccess={handleDocumentLoadSuccess}
                  onLoadError={handleDocumentLoadError}
                  loading={
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mr-3"></div>
                      <span className="text-gray-300">Loading document...</span>
                    </div>
                  }
                  error={
                    <div className="text-center py-8 text-red-400">
                      <p>Failed to load PDF document.</p>
                      <button
                        onClick={retryLoadPdf}
                        className="mt-2 px-4 py-2 bg-red-600 hover:bg-red-700 rounded text-sm text-white"
                      >
                        Retry
                      </button>
                    </div>
                  }
                >
                  <div className="relative">
                    <Page
                      key={`page_${pageNumber}`}
                      pageNumber={pageNumber}
                      width={Math.min(800, window.innerWidth * 0.6 - 100)}
                      className="shadow-lg mx-auto"
                      onLoadError={handlePageLoadError}
                      loading={
                        <div className="flex items-center justify-center py-8">
                          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mr-3"></div>
                          <span className="text-gray-300">Loading page...</span>
                        </div>
                      }
                      error={
                        <div className="text-center py-8 text-red-400">
                          <p>Failed to load page {pageNumber}.</p>
                        </div>
                      }
                    />
                    {/* AvatarWorld Overlay */}
                    <div className="absolute bottom-0 right-0 z-10">
                      <AvatarsWorld />
                    </div>
                  </div>
                </Document>
                {/* PDF Page Info */}
                <div className="flex justify-center items-center mt-4">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-gray-400">
                      Page {pageNumber} of {numPages || 1}
                    </span>
                    {numPages && numPages > 1 && (
                      <select
                        value={pageNumber}
                        onChange={(e) => {
                          const newPage = Number(e.target.value);
                          setPageNumber(newPage);
                          if (audioRef.current) {
                            audioRef.current.pause();
                            audioRef.current.currentTime = 0;
                            console.log('Paused audio due to page selection.');
                          }
                          setIsPlaying(false);
                          setIsTransitioning(true);
                          setTimeout(() => {
                            setCurrentSlideIndex(newPage - 1);
                            setIsTransitioning(false);
                            console.log(`Selected page ${newPage}`);
                          }, 2000);
                        }}
                        className="bg-gray-700 text-white text-sm rounded px-2 py-1 border border-gray-600 focus:border-blue-500 focus:outline-none"
                      >
                        {Array.from({ length: numPages }, (_, i) => i + 1).map((page) => (
                          <option key={page} value={page}>
                            {page}
                          </option>
                        ))}
                      </select>
                    )}
                  </div>
                </div>
              </div>
            )}
            {/* No PDF loaded state */}
            {!file && !loading && !error && (
              <div className="text-center py-12 text-gray-400">
                <p>No PDF file loaded. Please provide a valid file URL.</p>
              </div>
            )}
          </div>
          {/* Right Side - Narration Content */}
          <div className="w-96 bg-gray-800 border-l border-gray-700 flex flex-col">
            {/* Narration Content */}
            <div className="flex-1 p-6 overflow-auto">
              <div className="mb-4">
                <h3 className="text-xl font-semibold mb-4 text-white">Narration</h3>
                <div className="bg-gray-700 rounded-lg p-6 min-h-96 border border-gray-600">
                  {pageNumber === 1 ? (
                    <div className="text-gray-200 leading-relaxed">
                      <p className="text-base">{greeting || 'No greeting available.'}</p>
                    </div>
                  ) : slideData.length > 0 ? (
                    <div className="text-gray-200 leading-relaxed">
                      {slideData[currentSlideIndex - 1] ? (
                        <p className="text-base">
                          {slideData[currentSlideIndex - 1]}
                        </p>
                      ) : (
                        <p className="text-gray-400 italic">No narration available for this slide.</p>
                      )}
                    </div>
                  ) : (
                    <p className="text-gray-400 italic">No narration available for this slide.</p>
                  )}
                </div>
              </div>
              {/* Additional Slide Info */}
              <div className="bg-gray-700 rounded-lg p-4 border border-gray-600">
                <div className="flex justify-between items-center text-sm text-gray-400">
                  <span>Current Slide: {currentSlideIndex + 1}</span>
                  <span>Total Slides: {(slideData.length + 1) || 1}</span>
                </div>
                <div className="mt-2">
                  <div className="w-full bg-gray-600 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                      style={{
                        width:
                          slideData.length > 0 || greeting
                            ? `${((currentSlideIndex + 1) / (slideData.length + 1)) * 100}%`
                            : '0%'
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ErrorBoundary>
  );
}

export default Pdf;
