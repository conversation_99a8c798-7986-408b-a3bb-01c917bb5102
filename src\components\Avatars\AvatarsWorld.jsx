import React, { useEffect, useState } from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, PerspectiveCamera, Environment } from '@react-three/drei';
// import { LipSyncAvatar } from './LipSyncAvatar'; // Import your avatar
// import phonemeTimeline from './phonemeData.json'; // or dynamic data from TTS
import { Avatars } from './Avatars';

export default function AvatarsWorld({ phonemeTimeline }) {
  const [currentViseme, setCurrentViseme] = useState('neutral');
  const [expression, setExpression] = useState('neutral');

  useEffect(() => {
    let index = 0;
    const interval = setInterval(() => {
      if (index < phonemeTimeline?.length) {
        setCurrentViseme(phonemeTimeline[index].viseme);
        setExpression(phonemeTimeline[index].expression || 'neutral'); // Optional
        index++;
      } else {
        setCurrentViseme('neutral');
        clearInterval(interval);
      }
    }, 100); // adjust speed according to TTS output timing

    return () => clearInterval(interval);
  }, []);

  return (
    <Canvas shadows dpr={[1, 2]} camera={{ position: [1, 0, 1], fov: 20, near: 0.1, far: 100 }}>
      <PerspectiveCamera makeDefault position={[0, 0.1, 1]} fov={20} />
      <directionalLight
        castShadow
        position={[0, 2, 2]}
        intensity={0.6}
        shadow-mapSize-width={4240}
        shadow-mapSize-height={4240}
      />
      <directionalLight position={[2, 6, 5]} intensity={0.2} color="#FDF4DC" />
      <directionalLight position={[2, 6, 5]} intensity={0.3} color="#FDF4DC" />
      <Environment preset="sunset" />
      <mesh receiveShadow rotation={[-Math.PI / 2, 0, 0]} position={[0, 0.2, 0]}>
        <planeGeometry args={[10, 10]} />
        <shadowMaterial transparent opacity={0.2} />
      </mesh>
      <Avatars
        position={[0, -1.37, 0]}
        scale={[1.5, 1.5, 1.5]}
        currentViseme={currentViseme}
        expression={expression}
      />
      <OrbitControls
        enablePan={false}
        enableZoom={true}
        enableRotate={true}
        maxPolarAngle={Math.PI / 2}
      />
    </Canvas>
  );
}
