import React, { useEffect, useRef } from 'react';
import { useGLTF, useFBX } from '@react-three/drei';
import { useFrame } from '@react-three/fiber';
import { SkeletonUtils } from 'three-stdlib';
import * as THREE from 'three';

const visemeMap = {
  AA: { Jaw_Open: 0.5, Mouth_Close: -0.4 },
  AE: { Jaw_Open: 0.4, Mouth_Stretch_L: 0.3, Mouth_Stretch_R: 0.3 },
  AH: { Jaw_Open: 0.35, Mouth_Close: -0.25 },
  AO: { Jaw_Open: 0.4, Mouth_Pucker_Up_L: 0.3, Mouth_Pucker_Up_R: 0.3 },
  B: { Mouth_Close: 0.5, Mouth_Press_L: 0.4, Mouth_Press_R: 0.4 },
  P: { Mouth_Close: 0.5, Mouth_Press_L: 0.4, Mouth_Press_R: 0.4 },
  M: { Mouth_Close: 0.5, Mouth_Press_L: 0.4, Mouth_Press_R: 0.4 },
  CH: { Jaw_Open: 0.3, Mouth_Press_L: 0.3, Mouth_Press_R: 0.3 },
  JH: { Jaw_Open: 0.3, Mouth_Press_L: 0.3, Mouth_Press_R: 0.3 },
  SH: { Mouth_Shrug_Upper: 0.3, Mouth_Close: 0.4 },
  D: { Jaw_Open: 0.3, Tongue_Tip_Up: 0.3 },
  T: { Jaw_Open: 0.3, Tongue_Tip_Up: 0.3 },
  EH: { Mouth_Wide: 0.4, Mouth_Stretch_L: 0.3, Mouth_Stretch_R: 0.3 },
  ER: { Jaw_Open: 0.3, V_Tongue_Raise: 0.3, Mouth_Close: -0.3 },
  EY: { Mouth_Stretch_L: 0.3, Mouth_Stretch_R: 0.3 },
  F: { V_Dental_Lip: 0.5, Mouth_Close: -0.3 },
  V: { V_Dental_Lip: 0.5, Mouth_Close: -0.3 },
  G: { Jaw_Open: 0.3, Tongue_Backward: 0.3 },
  K: { Jaw_Open: 0.3, Tongue_Backward: 0.3 },
  NG: { Jaw_Open: 0.3, Tongue_Backward: 0.3 },
  HH: { Mouth_Close: -0.25 },
  IY: { Mouth_Smile_L: 0.4, Mouth_Smile_R: 0.4, Mouth_Wide: 0.4 },
  L: { Tongue_Tip_Up: 0.5 },
  N: { Mouth_Close: -0.3, Tongue_Tip_Up: 0.4 },
  OW: { Mouth_Pucker_Up_L: 0.4, Mouth_Pucker_Up_R: 0.4 },
  OY: { Mouth_Pucker_Up_L: 0.4, Mouth_Stretch_R: 0.3 },
  R: { Tongue_Curl_U: 0.5, Mouth_Close: -0.3 },
  S: { V_Tight: 0.5, Mouth_Shrug_Upper: 0.3 },
  Z: { V_Tight: 0.5, Mouth_Shrug_Upper: 0.3 },
  TH: { V_Tongue_Out: 0.5 },
  DH: { V_Tongue_Out: 0.5 },
  UW: { Mouth_Pucker_Up_L: 0.5, Mouth_Pucker_Up_R: 0.5 },
  W: { Mouth_Pucker_Up_L: 0.4, Mouth_Pucker_Up_R: 0.4 },
  Y: { Mouth_Smile_L: 0.3, Mouth_Smile_R: 0.3 },
  ',': { Mouth_Close: 0.3 },
  '.': { Mouth_Close: 0.3 },
  '-': { Mouth_Close: 0.3 },
  ' ': {}
};

const expressionMap = {
  neutral: {},
  smile: {
    Mouth_Smile_L: 0.3,
    Mouth_Smile_R: 0.3,
    Cheek_Raise_L: 0.2,
    Cheek_Raise_R: 0.2,
    Eye_Blink_L: 0.05,
    Eye_Blink_R: 0.05
  },
  sad: {
    Mouth_Frown_L: 0.3,
    Mouth_Frown_R: 0.3,
    Cheek_Suck_L: 0.2,
    Cheek_Suck_R: 0.2,
    Brow_Raise_Inner_L: 0.15,
    Brow_Raise_Inner_R: 0.15
  },
  angry: {
    Brow_Compress_L: 0.3,
    Brow_Compress_R: 0.3,
    Brow_Drop_L: 0.2,
    Brow_Drop_R: 0.2,
    Eye_Squint_L: 0.2,
    Eye_Squint_R: 0.2,
    Mouth_Tighten_L: 0.25,
    Mouth_Tighten_R: 0.25
  },
  surprised: {
    Brow_Raise_Outer_L: 0.25,
    Brow_Raise_Outer_R: 0.25,
    Eye_Wide_L: 0.25,
    Eye_Wide_R: 0.25,
    Jaw_Open: 0.2,
    Mouth_Open: 0.2
  },
  blink: { Eye_Blink_L: 1.0, Eye_Blink_R: 1.0 },
  confused: { Brow_Raise_Inner_L: 0.2, Brow_Drop_R: 0.2, Mouth_Shrug_Lower: 0.15 },
  excited: {
    Mouth_Smile_L: 0.4,
    Mouth_Smile_R: 0.4,
    Cheek_Raise_L: 0.3,
    Cheek_Raise_R: 0.3,
    Eye_Wide_L: 0.15,
    Eye_Wide_R: 0.15
  },
  disgust: { Nose_Sneer_L: 0.2, Nose_Sneer_R: 0.2, Mouth_Press_L: 0.2, Mouth_Press_R: 0.2 },
  thinking: {
    Brow_Raise_Inner_L: 0.1,
    Brow_Raise_Inner_R: 0.1,
    Mouth_Tighten_L: 0.15,
    Mouth_Tighten_R: 0.15
  },
  laugh: {
    Mouth_Smile_L: 0.5,
    Mouth_Smile_R: 0.5,
    Cheek_Raise_L: 0.3,
    Cheek_Raise_R: 0.3,
    Jaw_Open: 0.3
  },
  fear: {
    Eye_Wide_L: 0.4,
    Eye_Wide_R: 0.4,
    Brow_Raise_Outer_L: 0.3,
    Brow_Raise_Outer_R: 0.3,
    Mouth_Open: 0.3,
    Jaw_Open: 0.2
  },
  bored: {
    Brow_Drop_L: 0.1,
    Brow_Drop_R: 0.1,
    Eye_Blink_L: 0.2,
    Eye_Blink_R: 0.2,
    Mouth_Shrug_Lower: 0.15
  },
  focus: { Brow_Compress_L: 0.2, Brow_Compress_R: 0.2, Eye_Squint_L: 0.2, Eye_Squint_R: 0.2 },
  wink_left: { Eye_Blink_L: 1.0, Eye_Blink_R: 0.0 },
  wink_right: { Eye_Blink_L: 0.0, Eye_Blink_R: 1.0 }
};

export function Avatars({ currentViseme = 'neutral', expression = 'neutral', ...props }) {
  const { scene } = useGLTF('/avatar_1.glb');
  const { animations: animClips } = useFBX('/idle_sasthra_female.fbx');

  const clone = React.useMemo(() => SkeletonUtils.clone(scene), [scene]);
  const mixer = useRef();
  const nodes = {};
  clone.traverse((o) => {
    if (o.isMesh || o.isSkinnedMesh) nodes[o.name] = o;
  });

  const currentInfluences = useRef({});

  useEffect(() => {
    const scalp = nodes['outfit_outfit_0085_16'];
    if (scalp) scalp.visible = false;
  }, [nodes]);

  useEffect(() => {
    if (animClips.length > 0) {
      mixer.current = new THREE.AnimationMixer(clone);
      const action = mixer.current.clipAction(animClips[0]);
      action.play();
    }
  }, [animClips, clone]);

  useFrame((_, delta) => {
    mixer.current?.update(delta);
    const headMesh = nodes['outfit_outfit_0085_12'];
    if (!headMesh?.morphTargetInfluences || !headMesh.morphTargetDictionary) return;

    const dict = headMesh.morphTargetDictionary;
    const influences = headMesh.morphTargetInfluences;

    // Prepare target morph weights
    const targetMorphs = {};
    const viseme = visemeMap[currentViseme] || {};
    const exp = expressionMap[expression] || {};

    // Combine viseme and expression weights
    for (let key in dict) {
      const visemeVal = viseme[key] || 0;
      const expVal = exp[key] || 0;
      const target = Math.max(visemeVal, expVal);
      const current = currentInfluences.current[key] || 0;
      const smooth = 5 * delta;
      currentInfluences.current[key] = THREE.MathUtils.lerp(current, target, smooth);
      influences[dict[key]] = currentInfluences.current[key];
    }
  });

  return <primitive object={clone} {...props} />;
}

useGLTF.preload('/avatar_1.glb');
